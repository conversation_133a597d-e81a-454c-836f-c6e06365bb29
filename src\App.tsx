import { useState } from 'react'
import './App.css'
import { IconButton } from './components/icon-button/icon-button'
import { PrimaryButton } from './components/primary-button/primary-button'
import { SecondaryButton } from './components/secondary-button/secondary-button'
import { Slider } from './components/slider/slider'
import { SearchBox } from './components/search-box/search-box'
import { DropDown } from './components/drop-down/drop-down'
import { TabItem } from './components/tab-item/tab-item'
import { TabGroup } from './components/tab-group/tab-group'
import { Plus, Search, Settings, Heart, Download, Trash, Moon, Sun, Bell, Volume2, Sliders, Home, User, FileText, Image, File, Folder } from 'lucide-react'
import RenderPage from './pages/RenderPage'

function App() {
  const [count, setCount] = useState(0)
  const [theme, setTheme] = useState<'light' | 'dark'>('dark')
  const [showDemo, setShowDemo] = useState(false)

  return (
    <div className={`app-container theme-${theme}`}>
      {!showDemo ? (
        <div className="render-page-container">
          <RenderPage />
        </div>
      ) : (
        <>

      <header>
        <h1>慧通组件库 - 图标按钮展示</h1>
        <IconButton 
          icon={theme === 'dark' ? Sun : Moon} 
          onClick={() => setTheme(theme === 'dark' ? 'light' : 'dark')}
          size="large"
        />
      </header>

      <div className="demo-section">
        <h2>基本图标按钮</h2>
        <div className="buttons-row">
          <IconButton icon={Plus} onClick={() => alert('添加')} />
          <IconButton icon={Search} onClick={() => alert('搜索')} />
          <IconButton icon={Settings} onClick={() => alert('设置')} />
          <IconButton icon={Heart} onClick={() => alert('收藏')} />
        </div>
      </div>

      <div className="demo-section">
        <h2>不同尺寸</h2>
        <div className="buttons-row">
          <IconButton icon={Plus} size="small" />
          <IconButton icon={Plus} size="medium" />
          <IconButton icon={Plus} size="large" />
        </div>
      </div>

      <div className="demo-section">
        <h2>禁用状态</h2>
        <div className="buttons-row">
          <IconButton icon={Trash} disabled />
          <IconButton icon={Download} disabled />
        </div>
      </div>

      <div className="demo-section">
        <h2>按钮计数器</h2>
        <div className="buttons-row counter-demo">
          <IconButton icon={Plus} onClick={() => setCount(count + 1)} />
          <span className="counter">{count}</span>
          <IconButton 
            icon={Trash} 
            onClick={() => setCount(0)}
            disabled={count === 0}
          />
        </div>
      </div>

      <div className="demo-section">
        <h2>通知图标</h2>
        <div className="buttons-row">
          <div className="notification-button">
            <IconButton icon={Bell} onClick={() => alert('通知')} />
            <span className="badge">3</span>
          </div>
        </div>
      </div>

      <div className="demo-section">
        <h2>主要按钮 (Primary Button)</h2>
        <div className="buttons-row">
          <PrimaryButton icon={Plus}>按钮</PrimaryButton>
          <PrimaryButton icon={Download}>下载</PrimaryButton>
          <PrimaryButton disabled>禁用按钮</PrimaryButton>
          <PrimaryButton size="small">小按钮</PrimaryButton>
          <PrimaryButton size="large">大按钮</PrimaryButton>
        </div>
      </div>

      <div className="demo-section">
        <h2>次级按钮 (Secondary Button)</h2>
        <div className="buttons-row">
          <SecondaryButton icon={Plus}>按钮</SecondaryButton>
          <SecondaryButton icon={Settings}>设置</SecondaryButton>
          <SecondaryButton disabled>禁用按钮</SecondaryButton>
          <SecondaryButton size="small">小按钮</SecondaryButton>
          <SecondaryButton size="large">大按钮</SecondaryButton>
        </div>
      </div>

      <div className="demo-section">
        <h2>滑块控件 (Slider)</h2>
        <div className="slider-demo">
          <h3>基本滑块</h3>
          <Slider defaultValue={50} onChange={(value) => console.log('基本滑块值变化:', value)} />
        </div>
        
        <div className="slider-demo">
          <h3>音量控制</h3>
          <div className="volume-slider">
            <Volume2 size={18} />
            <Slider 
              defaultValue={75} 
              width={200} 
              onChange={(value) => console.log('音量:', value)} 
            />
          </div>
        </div>

        <div className="slider-demo">
          <h3>自定义步长</h3>
          <Slider 
            min={0} 
            max={10} 
            step={0.5} 
            defaultValue={5} 
            onChange={(value) => console.log('步长0.5的滑块:', value)} 
          />
        </div>

        <div className="slider-demo">
          <h3>禁用状态</h3>
          <Slider defaultValue={30} disabled />
        </div>
      </div>

      <div className="demo-section">
        <h2>搜索框 (Search Box)</h2>
        <div className="search-demo">
          <h3>基本搜索框</h3>
          <SearchBox 
            placeholder="搜索"
            onChange={(value) => console.log('搜索值变化:', value)}
            onSearch={(value) => console.log('执行搜索:', value)}
          />
        </div>
        
        <div className="search-demo">
          <h3>自定义宽度</h3>
          <SearchBox 
            width={300}
            placeholder="请输入关键词"
            onSearch={(value) => console.log('执行搜索:', value)}
          />
        </div>

        <div className="search-demo">
          <h3>默认值</h3>
          <SearchBox 
            defaultValue="默认搜索内容"
            onSearch={(value) => console.log('执行搜索:', value)}
          />
        </div>

        <div className="search-demo">
          <h3>禁用状态</h3>
          <SearchBox 
            placeholder="禁用搜索框"
            disabled
          />
        </div>
      </div>

      <div className="demo-section">
        <h2>下拉框 (Drop Down)</h2>
        <div className="dropdown-demo">
          <h3>基本下拉框</h3>
          <DropDown 
            options={[
              { value: '1', label: '选项一' },
              { value: '2', label: '选项二' },
              { value: '3', label: '选项三' },
            ]}
            placeholder="请选择"
            onChange={(value, option) => console.log('下拉框值变化:', value, option)}
          />
        </div>
        
        <div className="dropdown-demo">
          <h3>自定义宽度</h3>
          <DropDown 
            width={300}
            options={[
              { value: '1', label: '选项一' },
              { value: '2', label: '选项二' },
              { value: '3', label: '选项三' },
            ]}
            placeholder="请选择一个选项"
          />
        </div>

        <div className="dropdown-demo">
          <h3>默认选中</h3>
          <DropDown 
            defaultValue="2"
            options={[
              { value: '1', label: '选项一' },
              { value: '2', label: '选项二' },
              { value: '3', label: '选项三' },
            ]}
          />
        </div>

        <div className="dropdown-demo">
          <h3>禁用选项</h3>
          <DropDown 
            options={[
              { value: '1', label: '正常选项' },
              { value: '2', label: '禁用选项', disabled: true },
              { value: '3', label: '正常选项' },
            ]}
            placeholder="包含禁用选项"
          />
        </div>

        <div className="dropdown-demo">
          <h3>不同尺寸</h3>
          <div style={{ display: 'flex', gap: '16px', alignItems: 'flex-start' }}>
            <DropDown 
              size="small"
              options={[
                { value: '1', label: '选项一' },
                { value: '2', label: '选项二' },
              ]}
              placeholder="小尺寸"
              width={120}
            />
            <DropDown 
              options={[
                { value: '1', label: '选项一' },
                { value: '2', label: '选项二' },
              ]}
              placeholder="默认尺寸"
              width={120}
            />
            <DropDown 
              size="large"
              options={[
                { value: '1', label: '选项一' },
                { value: '2', label: '选项二' },
              ]}
              placeholder="大尺寸"
              width={120}
            />
          </div>
        </div>

        <div className="dropdown-demo">
          <h3>禁用状态</h3>
          <DropDown 
            disabled
            options={[
              { value: '1', label: '选项一' },
              { value: '2', label: '选项二' },
            ]}
            placeholder="禁用下拉框"
          />
        </div>
      </div>
      
      <div className="demo-section">
        <h2>标签项 (Tab Item)</h2>
        
        <div className="tab-demo">
          <h3>默认状态</h3>
          <div className="tab-row" style={{ display: 'flex', gap: '16px' }}>
            <TabItem
              label="模型"
              icon={FileText}
              onClick={() => console.log('点击模型标签')}
            />
            <TabItem
              label="设置"
              icon={Settings}
              onClick={() => console.log('点击设置标签')}
            />
            <TabItem
              label="首页"
              icon={Home}
              onClick={() => console.log('点击首页标签')}
            />
          </div>
        </div>
        
        <div className="tab-demo">
          <h3>选中状态</h3>
          <div className="tab-row" style={{ display: 'flex', gap: '16px', marginTop: '16px' }}>
            <TabItem
              label="模型"
              icon={FileText}
              isActive={true}
            />
            <TabItem
              label="设置"
              icon={Settings}
            />
            <TabItem
              label="首页"
              icon={Home}
            />
          </div>
        </div>
        
        <div className="tab-demo">
          <h3>自定义宽度</h3>
          <div className="tab-row" style={{ display: 'flex', gap: '16px', marginTop: '16px' }}>
            <TabItem
              label="短标签"
              icon={Sliders}
              width={80}
            />
            <TabItem
              label="较长的标签项"
              icon={User}
              width={150}
            />
          </div>
        </div>
        
        <div className="tab-demo">
          <h3>无图标标签</h3>
          <div className="tab-row" style={{ display: 'flex', gap: '16px', marginTop: '16px' }}>
            <TabItem
              label="选项一"
            />
            <TabItem
              label="选项二"
              isActive={true}
            />
            <TabItem
              label="选项三"
            />
          </div>
        </div>
      </div>
      
      <div className="demo-section">
        <h2>标签组 (Tab Group) - 点击切换状态</h2>
        
        <div className="tab-demo">
          <h3>基本使用</h3>
          <TabGroup>
            <TabItem
              label="模型"
              icon={FileText}
            />
            <TabItem
              label="设置"
              icon={Settings}
            />
            <TabItem
              label="首页"
              icon={Home}
            />
          </TabGroup>
        </div>
        
        <div className="tab-demo">
          <h3>自定义间距</h3>
          <TabGroup gap={24}>
            <TabItem
              label="图片"
              icon={Image}
            />
            <TabItem
              label="文件"
              icon={File}
            />
            <TabItem
              label="文件夹"
              icon={Folder}
            />
          </TabGroup>
        </div>
        
        <div className="tab-demo">
          <h3>默认选中索引</h3>
          <TabGroup defaultActiveIndex={1}>
            <TabItem
              label="选项一"
            />
            <TabItem
              label="选项二"
            />
            <TabItem
              label="选项三"
            />
          </TabGroup>
        </div>
        
        <div className="tab-demo">
          <h3>带状态变化回调</h3>
          <TabGroup onChange={(index) => console.log('当前选中索引:', index)}>
            <TabItem
              label="监听状态变化的标签一"
              width={160}
            />
            <TabItem
              label="监听状态变化的标签二"
              width={160}
            />
          </TabGroup>
        </div>
      </div>
        </>
      )}
    </div>
  )
}

export default App
