import React, { useState } from 'react';
import { Pipette, Plus } from 'lucide-react';
import { HexColorPicker } from 'react-colorful';
import './custom-material-panel.css';
import { Slider } from '../slider/slider';

interface CustomMaterialPanelProps {
  onChange?: (material: MaterialSettings) => void;
  defaultSettings?: MaterialSettings;
}

export interface MaterialSettings {
  color: string;
  metalness: number;
  roughness: number;
  glass: number;
  textureUrl?: string;
}

export const CustomMaterialPanel: React.FC<CustomMaterialPanelProps> = ({ 
  onChange, 
  defaultSettings
}) => {
  // 设置默认值
  const defaultColor = defaultSettings?.color || '#B39B9C';
  const defaultMetalness = defaultSettings?.metalness || 50;
  const defaultRoughness = defaultSettings?.roughness || 50;
  const defaultGlass = defaultSettings?.glass || 50;

  // 状态
  const [color, setColor] = useState<string>(defaultColor);
  const [metalness, setMetalness] = useState<number>(defaultMetalness);
  const [roughness, setRoughness] = useState<number>(defaultRoughness);
  const [glass, setGlass] = useState<number>(defaultGlass);
  const [textureUrl, setTextureUrl] = useState<string | undefined>(defaultSettings?.textureUrl);

  // 更新材质设置
  const updateSettings = (
    newColor?: string, 
    newMetalness?: number, 
    newRoughness?: number, 
    newGlass?: number, 
    newTextureUrl?: string
  ) => {
    const updatedSettings: MaterialSettings = {
      color: newColor || color,
      metalness: newMetalness || metalness,
      roughness: newRoughness || roughness,
      glass: newGlass || glass,
      textureUrl: newTextureUrl || textureUrl
    };

    if (onChange) {
      onChange(updatedSettings);
    }
  };

  // 处理颜色变化
  const handleColorChange = (newColor: string) => {
    setColor(newColor);
    updateSettings(newColor);
  };

  // 处理文件上传
  const handleFileUpload = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (!file) return;
    
    const url = URL.createObjectURL(file);
    setTextureUrl(url);
    updateSettings(undefined, undefined, undefined, undefined, url);
  };

  // 使用吸管工具
  const handleEyeDropper = async () => {
    if ('EyeDropper' in window) {
      try {
        // @ts-expect-error - EyeDropper API 可能不在所有TypeScript类型中
        const eyeDropper = new window.EyeDropper();
        const result = await eyeDropper.open();
        setColor(result.sRGBHex);
        updateSettings(result.sRGBHex);
      } catch (error) {
        console.error('EyeDropper error:', error);
      }
    } else {
      console.warn('EyeDropper API not supported');
    }
  };

  return (
    <div className="custom-material" data-layer="自定义材质">
      <div className="material-property-group" data-layer="Frame 37">
        <div className="property-label" data-layer="颜色">颜色</div>
        <div className="color-picker-container">
          <HexColorPicker color={color} onChange={handleColorChange} />
          <Pipette 
            className="color-pipette-icon" 
            onClick={handleEyeDropper} 
            data-states="default" 
          />
        </div>
      </div>
      
      <div className="material-property-group" data-layer="Frame 36">
        <div className="property-label" data-layer="金属度">金属度</div>
        <Slider
          defaultValue={metalness}
          onChange={(value) => {
            setMetalness(value);
            updateSettings(undefined, value);
          }}
          showValue={false}
          width="100%"
        />
      </div>
      
      <div className="material-property-group" data-layer="Frame 34">
        <div className="property-label" data-layer="粗糙度">粗糙度</div>
        <Slider
          defaultValue={roughness}
          onChange={(value) => {
            setRoughness(value);
            updateSettings(undefined, undefined, value);
          }}
          showValue={false}
          width="100%"
        />
      </div>
      
      <div className="material-property-group" data-layer="Frame 35">
        <div className="property-label" data-layer="玻璃">玻璃</div>
        <Slider
          defaultValue={glass}
          onChange={(value) => {
            setGlass(value);
            updateSettings(undefined, undefined, undefined, value);
          }}
          showValue={false}
          width="100%"
        />
      </div>
      
      <div className="material-property-group" data-layer="Frame 43">
        <div className="property-label" data-layer="纹理贴图">纹理贴图</div>
        <label className="texture-upload-area" data-layer="Frame 32">
          {textureUrl ? (
            <img src={textureUrl} alt="纹理贴图" className="texture-preview" />
          ) : (
            <>
              <div className="plus-icon" data-layer="plus">
                <Plus size={20} color="var(--color-content-regular)" />
              </div>
              <div className="upload-text" data-layer="上传图片">上传图片</div>
            </>
          )}
          <input 
            type="file" 
            accept="image/*" 
            onChange={handleFileUpload} 
            style={{ display: 'none' }} 
          />
        </label>
      </div>
    </div>
  );
};
