.primary-button {
  align-items: center;
  background-color: var(--color-brand);
  border: none;
  border-radius: var(--radius-m);
  color: var(--color-content-invert);
  cursor: pointer;
  display: inline-flex;
  font-size: var(--font-size);
  font-weight: 500;
  gap: 8px;
  height: 32px;
  justify-content: center;
  padding: 6px 12px;
  position: relative;
  transition: background-color 0.2s ease, opacity 0.2s ease;
  white-space: nowrap;
}

/* 图标样式 */
.primary-button__icon {
  height: 16px;
  width: 16px;
}

/* 悬停状态 */
.primary-button:hover:not(:disabled) {
  background-color: var(--color-brand-hover);
}

/* 禁用状态 */
.primary-button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

/* 激活状态 */
.primary-button:active:not(:disabled) {
  background-color: var(--color-brand-hover);
  transform: translateY(1px);
}

/* 满宽模式 */
.primary-button--full-width {
  width: 100%;
}
