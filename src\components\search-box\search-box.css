.search-box {
  padding: 6px 8px;
  background: var(--color-input-background, rgba(0, 0, 0, 0.2));
  border-radius: var(--radius-m, 8px);
  outline: 1px var(--color-border, rgba(255, 255, 255, 0.12)) solid;
  outline-offset: -1px;
  display: inline-flex;
  justify-content: flex-start;
  align-items: center;
  gap: 8px;
  height: 32px;
  box-sizing: border-box;
  cursor: text;
}

.search-box__icon-container {
  width: 16px;
  height: 16px;
  position: relative;
  overflow: hidden;
  display: flex;
  justify-content: center;
  align-items: center;
  cursor: pointer;
}

.search-box__icon {
  color: var(--color-content-mute, rgba(255, 255, 255, 0.40));
  stroke-width: 1px;
}

.search-box__input {
  flex-grow: 1;
  border: none;
  background: transparent;
  outline: none;
  padding: 0;
  color: var(--color-content-accent);
  font-size: var(--font-size);
  font-family: '<PERSON>Fang SC', -apple-system, BlinkMacSystemFont, '<PERSON><PERSON><PERSON> UI', <PERSON><PERSON>, 'Helvetica Neue', Arial, sans-serif;
  font-weight: 500;
}

.search-box__input::placeholder {
  color: var(--color-content-mute, rgba(255, 255, 255, 0.40));
}

/* 禁用状态 */
.search-box--disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.search-box--disabled .search-box__input {
  cursor: not-allowed;
  pointer-events: none;
}

.search-box--disabled .search-box__icon-container {
  cursor: not-allowed;
}

/* 适配深色/浅色主题 */
.theme-light .search-box__icon {
  color: var(--color-content-mute);
}

.theme-light .search-box__input::placeholder {
  color: var(--color-content-mute);
}
