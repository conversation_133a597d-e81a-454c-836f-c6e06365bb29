.slider {
  display: flex;
  flex-direction: column;
  gap: 12px;
  cursor: pointer;
  position: relative;
  height: 32px;
}

.slider__bg,
.slider__drag {
  height: 32px;
  position: absolute;
  border-radius: var(--radius-m);
  left: 0;
  top: 0;
}

.slider__bg {
  width: 100%;
  background: var(--color-input-background);
}

.slider__drag {
  background: var(--color-content-secondary);
  /* width is dynamically set in TSX */
}

/* 数值样式 */
.slider__value {
  font-size: var(--font-size);
  color: var(--color-content-secondary);
  text-align: left;
  margin-top: 8px;
  user-select: none;
}

/* 禁用状态 */
.slider--disabled {
  opacity: 0.5;
  pointer-events: none;
}

/* 方便页面布局的样式 */
.slider-demo {
  padding: 20px;
  max-width: 400px;
  margin-bottom: 24px;
}

/* 适配主题变量 */
:root {
  --color-container-bg: var(--color-container-background);
}

/* 布局样式 */
.volume-slider {
  display: flex;
  align-items: center;
  gap: 16px;
}

.volume-slider svg {
  color: var(--color-content-secondary);
}
