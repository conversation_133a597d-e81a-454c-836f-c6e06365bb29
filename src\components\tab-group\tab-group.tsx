import React, { useState, Children, cloneElement } from 'react';
import type { ReactElement } from 'react';
import './tab-group.css';
import { TabItem } from '../tab-item/tab-item';
import type { TabItemProps } from '../tab-item/tab-item';

export interface TabGroupProps {
  /** 初始激活的选项卡索引，默认为0（第一个选项卡） */
  defaultActiveIndex?: number;
  /** 子组件，应为TabItem组件 */
  children: React.ReactNode;
  /** 点击选项卡时的回调 */
  onChange?: (activeIndex: number) => void;
  /** 自定义类名 */
  className?: string;
  /** 间距，默认为16px */
  gap?: number | string;
}

export const TabGroup: React.FC<TabGroupProps> = ({
  defaultActiveIndex = 0,
  children,
  onChange,
  className = '',
  gap = '16px',
}) => {
  const [activeIndex, setActiveIndex] = useState<number>(defaultActiveIndex);
  
  // 确保传入的索引在合理范围内
  const childrenArray = Children.toArray(children).filter(
    (child) => React.isValidElement(child) && child.type === TabItem
  );
  
  // 点击选项卡的处理函数
  const handleTabClick = (index: number) => {
    setActiveIndex(index);
    if (onChange) {
      onChange(index);
    }
  };
  
  // 设置tab-group的样式
  const groupStyle = {
    gap: typeof gap === 'number' ? `${gap}px` : gap,
  };
  
  return (
    <div className={`tab-group ${className}`} style={groupStyle}>
      {Children.map(childrenArray, (child, index) => {
        // 只处理TabItem类型的子元素
        if (React.isValidElement<TabItemProps>(child)) {
          // 克隆子元素并附加额外的props
          return cloneElement(child as ReactElement<TabItemProps>, {
            isActive: index === activeIndex,
            onClick: () => handleTabClick(index),
          });
        }
        return child;
      })}
    </div>
  );
};
