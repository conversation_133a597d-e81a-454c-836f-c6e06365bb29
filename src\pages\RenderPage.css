.render-page {
  width: 100vw;
  height: 100vh;
  margin: 0;
  padding: 20px;
  box-sizing: border-box;
  background: var(--color-background, #1A1A1A);
  overflow: hidden;
  display: flex;
  flex-direction: column;
  color: var(--color-content-accent);
  font-family: "PingFang SC", -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Oxygen, Ubuntu, Cantarell, "Open Sans", "Helvetica Neue", sans-serif;
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
}

.title-bar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-left: 8px;
  width: 100%;
  margin-bottom: 20px;
}

.logo {
  width: 78px;
  height: 14px;
  object-fit: contain;
}

.user-controls {
  display: flex;
  justify-content: flex-end;
  align-items: center;
  gap: 8px;
}

.render-container {
  display: flex;
  flex: 1;
  gap: 20px;
  overflow: hidden;
  padding: 0;
  box-sizing: border-box;
  max-width: 100%;
  width: 100%;
  margin: 0;
}

.render-window {
  flex: 1;
  min-width: 0;
  background: var(--color-primary-background);
  border-radius: 16px;
  position: relative;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.button-container {
  position: absolute;
  right: 20px;
  bottom: 20px;
  display: flex;
  justify-content: flex-end;
  align-items: center;
  gap: 8px;
}

.control-button {
  padding: 4px 8px;
  display: flex;
  align-items: center;
  gap: 6px;
  background: var(--backgrounds-input-background, rgba(0, 0, 0, 0.20));
  border-radius: 99px;
  cursor: pointer;
}

.control-button span {
  color: var(--contents-content-regular, rgba(255, 255, 255, 0.70));
  font-size: var(--font-size);
  font-weight: 500;
}

.icon-wrapper {
  width: 16px;
  height: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--contents-content-regular, rgba(255, 255, 255, 0.70));
}

.property-panel {
  /* 220px = 60*3 + 8*2 + 12*2，正好一行放3个材质球 */
  width: 220px;
  flex-shrink: 0;
  padding: 12px;
  background: var(--color-primary-background);
  border-radius: 16px;
  display: flex;
  flex-direction: column;
  gap: 16px;
  overflow: hidden;
}

.panel-section {
  display: flex;
  flex-direction: column;
  gap: 8px;
  width: 100%;
}

.section-header {
  display: flex;
  align-items: center;
  gap: 4px;
}

.section-header span {
  color: var(--contents-content-regular, rgba(255, 255, 255, 0.70));
  font-size: var(--font-size);
  font-weight: 500;
}

.dropdown-wrapper,
.upload-button-wrapper,
.search-wrapper {
  width: 100%;
}

.bottom-buttons {
  margin-top: auto;
  display: flex;
  justify-content: center;
  gap: 8px;
  padding-top: 12px;
}

.material-thumbnails {
  display: flex;
  gap: 8px;
}

.material-thumbnail {
  width: 40px;
  height: 40px;
  background: var(--backgrounds-input-background, rgba(255, 255, 255, 0.05));
  border-radius: 8px;
}

.material-thumbnail.active {
  border: 1px solid var(--brand, #2269EC);
}

.tab-switch {
  height: 36px;
  padding: 4px;
  background: var(--backgrounds-input-background, rgba(255, 255, 255, 0.05));
  border-radius: 8px;
  display: flex;
  align-items: center;
  gap: 4px;
}

.tab-item {
  flex: 1;
  height: 28px;
  display: flex;
  justify-content: center;
  align-items: center;
  border-radius: 6px;
  color: var(--contents-content-regular, rgba(255, 255, 255, 0.70));
  font-size: var(--font-size);
  font-weight: 500;
  cursor: pointer;
}

.tab-item.active {
  background: var(--support, rgba(255, 255, 255, 0.12));
  color: var(--contents-content-accent, rgba(255, 255, 255, 0.90));
}

.materials-container {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.materials-grid {
  display: flex;
  align-content: flex-start;
  flex-wrap: wrap;
  gap: 8px;
  height: 100%;
}

.material-item {
  width: 60px;
  height: 60px;
  background: var(--backgrounds-input-background, rgba(255, 255, 255, 0.05));
  border-radius: 8px;
  cursor: pointer;
}

.materials-container {
  display: flex;
  flex-direction: column;
  gap: 12px;
  flex: 1;
  min-height: 0;
  overflow: hidden;
}

/* 添加滚动条样式 */
/* .materials-grid::-webkit-scrollbar 已移除，不显示滚动条 */

/* .materials-grid::-webkit-scrollbar-track 及 .materials-grid::-webkit-scrollbar-thumb 已移除，不显示滚动条 */

.content-area {
  display: none;
}

.preview-area {
  width: 100%;
  height: 100%;
  background: var(--backgrounds-input-background, rgba(0, 0, 0, 0.20));
  border-radius: var(--radius-m);
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: rgba(0, 0, 0, 0.3);
  background-image: linear-gradient(45deg, rgba(0, 0, 0, 0.1) 25%, transparent 25%, transparent 75%, rgba(0, 0, 0, 0.1) 75%),
                    linear-gradient(45deg, rgba(0, 0, 0, 0.1) 25%, transparent 25%, transparent 75%, rgba(0, 0, 0, 0.1) 75%);
  background-size: 20px 20px;
  background-position: 0 0, 10px 10px;
  overflow: hidden;
  position: relative;
}

.preview-area::after {
  content: '';
  position: absolute;
  inset: 0;
  background: linear-gradient(to bottom right, transparent, rgba(34, 105, 236, 0.1));
  pointer-events: none;
}

.placeholder-preview {
  color: var(--color-content-mute);
  font-size: var(--font-size);
}

.model-preview {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
}

.model-preview-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 24px;
  width: 100%;
}

.model-preview-shape {
  width: 200px;
  height: 200px;
  border-radius: 8px;
  background: linear-gradient(135deg, var(--color-brand), var(--color-brand-hover));
  position: relative;
  transform-style: preserve-3d;
  transform: perspective(800px) rotateY(15deg) rotateX(10deg);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.4);
  transition: transform 0.5s ease, box-shadow 0.5s ease;
}

.model-preview-shape:hover {
  transform: perspective(800px) rotateY(25deg) rotateX(15deg) translateY(-10px);
  box-shadow: 0 30px 50px rgba(0, 0, 0, 0.5);
}

.model-preview-reflection {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(255,255,255,0.3), transparent);
  border-radius: 8px;
}

.render-stats {
  display: flex;
  gap: 20px;
  background-color: rgba(0, 0, 0, 0.3);
  padding: 12px 24px;
  border-radius: var(--radius-m);
  backdrop-filter: blur(10px);
}

.render-stat-item {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.stat-label {
  font-size: var(--font-size);
  color: var(--color-content-mute);
  margin-bottom: 4px;
}

.stat-value {
  font-size: var(--font-size);
  color: var(--color-content-accent);
  font-weight: 500;
}

.button-group {
  display: flex;
  gap: 12px;
  margin-top: 24px;
}

.model-list {
  background-color: var(--color-dialog-background);
  border-radius: var(--radius-m);
  padding: 16px 24px;
}

.model-list h3 {
  color: var(--color-content-accent);
  margin-top: 0;
  margin-bottom: 16px;
}

.model-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: 16px;
}

.model-card {
  background-color: rgba(0, 0, 0, 0.2);
  border-radius: var(--radius-m);
  overflow: hidden;
  transition: all 0.3s ease;
  border: 1px solid transparent;
}

.model-card:hover {
  transform: translateY(-4px) scale(1.02);
  cursor: pointer;
  border-color: var(--color-brand);
  box-shadow: 0 8px 20px rgba(34, 105, 236, 0.15);
}



.model-thumbnail {
  height: 120px;
  background: linear-gradient(135deg, var(--color-brand), var(--color-brand-hover));
  opacity: 0.8;
  position: relative;
  overflow: hidden;
}

.model-thumbnail::before {
  content: '';
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: radial-gradient(circle at center, rgba(255, 255, 255, 0.2) 0%, transparent 60%);
  animation: shimmer 5s infinite linear;
}

@keyframes shimmer {
  0% { transform: translate(-50%, -50%) rotate(0deg); }
  100% { transform: translate(-50%, -50%) rotate(360deg); }
}

.model-info {
  padding: 12px;
}

.model-info h4 {
  margin: 0 0 8px 0;
  color: var(--color-content-accent);
}

.model-info p {
  margin: 0;
  font-size: var(--font-size);
  color: var(--color-content-mute);
}
