import { useState } from 'react';
import './RenderPage.css';
import { SearchBox } from '../components/search-box/search-box';
import { PrimaryButton } from '../components/primary-button/primary-button';
import { SecondaryButton } from '../components/secondary-button/secondary-button';
import { DropDown } from '../components/drop-down/drop-down';
import { CustomMaterialPanel } from '../components/custom-material-panel/custom-material-panel';
import { Copy, Download, FileText, Settings, Upload, HelpCircle } from 'lucide-react';

const RenderPage = () => {
  const [selectedModel, setSelectedModel] = useState('扫地机器人');
  const [searchQuery, setSearchQuery] = useState('');
  const [activeTab, setActiveTab] = useState('会通材料');
  const [materialSettings, setMaterialSettings] = useState({
    color: '#B39B9C',
    metalness: 50,
    roughness: 50,
    glass: 50
  });

  return (
    <div className="render-page">
      <div className="title-bar">
        <img className="logo" src="public/images/Logo.png" alt="RINKO"/>
        <div className="user-controls">
          <SecondaryButton icon={Copy}>复制图片</SecondaryButton>
          <PrimaryButton icon={Download}>保存图片</PrimaryButton>
        </div>
      </div>

      <div className="render-container">
        <div className="render-window">
          {/* 3D渲染区域 */}
          <div className="render-area">
            {/* 这里放置3D渲染内容 */}
          </div>
          
          <div className="button-container">
            <div className="control-button">
              <div className="icon-wrapper">
                <FileText size={16} />
              </div>
              <span>默认视图</span>
            </div>
            <div className="control-button">
              <div className="icon-wrapper">
                <HelpCircle size={16} />
              </div>
              <span>操作说明</span>
            </div>
          </div>
        </div>
        
        {/* 属性面板 */}
        <div className="property-panel">
          <div className="panel-section">
            <div className="section-header">
              <div className="icon-wrapper">
                <FileText size={16} />
              </div>
              <span>模型</span>
            </div>
            <div className="dropdown-wrapper">
              <DropDown
                options={[
                  { value: '扫地机器人', label: '扫地机器人' },
                  { value: '咖啡机', label: '咖啡机' },
                  { value: '电脑', label: '电脑' },
                  { value: '手机', label: '手机' }
                ]}
                value={selectedModel}
                onChange={(value) => setSelectedModel(value as string)}
              />
            </div>
            <div className="upload-button-wrapper">
              <SecondaryButton icon={Upload} fullWidth>上传模型</SecondaryButton>
            </div>
          </div>
          
          <div className="panel-section" style={{ flex: 1 }}>
            <div className="section-header">
              <div className="icon-wrapper">
                <Settings size={16} />
              </div>
              <span>材质设置</span>
            </div>
            
            <div className="material-thumbnails">
              <div className="material-thumbnail active"></div>
              <div className="material-thumbnail"></div>
            </div>
            
            <div className="tab-switch">
              <div 
                className={`tab-item ${activeTab === '会通材料' ? 'active' : ''}`} 
                onClick={() => setActiveTab('会通材料')}
              >
                会通材料
              </div>
              <div 
                className={`tab-item ${activeTab === '自定义' ? 'active' : ''}`}
                onClick={() => setActiveTab('自定义')}
              >
                自定义
              </div>
            </div>
            
            <div className="materials-container">
              {activeTab === '会通材料' ? (
                <>
                  <div className="search-wrapper">
                    <SearchBox 
                      placeholder="搜索" 
                      value={searchQuery} 
                      onChange={(value) => setSearchQuery(value)} 
                      onSearch={() => console.log('搜索:', searchQuery)}
                    />
                  </div>
                  
                  <div className="materials-grid">
                    {Array(24).fill(0).map((_, index) => (
                      <div key={index} className="material-item"></div>
                    ))}
                  </div>
                </>
              ) : (
                <CustomMaterialPanel
                  defaultSettings={materialSettings}
                  onChange={(settings) => {
                    setMaterialSettings(settings);
                    console.log('材质设置更新:', settings);
                    // 这里可以添加更新3D渲染的逻辑
                  }}
                />
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default RenderPage;
